<?php

namespace App\Modules\Transfer\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Transfer\Services\JobTransferService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\Domain\DomainParser;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class SendEppTransferRequestResponse implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * if process takes longer than indicated  timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct($transferId, $domainName, $domainId, $regDomainId, $userId, $email, $action)
    {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'transferId' => $transferId,
            'domainName' => $domainName,
            'domainId' => $domainId,
            'registeredDomainId' => $regDomainId,
            'userId' => $userId,
            'email' => $email,
            'action' => $action,
        ];

        $this->onConnection(QueueConnection::DOMAIN_TRANSFER_RESPONSE);
        $this->onQueue(QueueTypes::DOMAIN_TRANSFER_RESPONSE[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return $this->params['registeredDomainId'];
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            JobTransferService::instance()->userRequestAction($this->params);
        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho($e->getMessage(), $this->params['email']));

            if (strcmp($e->getMessage(), QueueErrorTypes::RETRY) === 0) {
                $this->retry();

                return;
            }

            $this->fail();
        }
    }

    private function failed(?Throwable $exception): void
    {
        // Send user notification of failure, etc...
    }

    private function retry(): void
    {
        $status = DomainStatus::PENDING;
        $type = QueueConnection::DOMAIN_TRANSFER_RESPONSE;
        $jobId = $this->params['domainId'];

        JobTransferService::instance()->addRetryLogs($type, $status, $this->params, $jobId);
    }
}
