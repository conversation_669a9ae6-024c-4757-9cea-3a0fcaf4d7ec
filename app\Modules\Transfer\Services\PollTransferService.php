<?php

namespace App\Modules\Transfer\Services;

use App\Events\DomainHistoryEvent;
use App\Modules\Client\Jobs\ScheduleDomainExpiryNotice;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Services\DomainService;
use App\Modules\Domain\Services\EppDomainService;
use App\Modules\Epp\Constants\EppDomainStatus;
use App\Modules\Notification\Services\TransferNotificationService;
use App\Modules\Payment\Constants\PaymentNodeStatus;
use App\Modules\Payment\Services\PaymentNodeService;
use App\Modules\Transfer\Constants\TransferRequest;
use App\Modules\Transfer\Services\EppTransferService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueErrorTypes;
use Exception;
use Illuminate\Support\Facades\DB;
use stdClass;
use Illuminate\Support\Facades\Mail;
use App\Mail\OutboundRequestMail;
use App\Events\EmailSent;
use App\Mail\Constants\MailConstant;
use App\Mail\Constants\Links;
use App\Mail\Constants\MailDetails;
use Illuminate\Support\Facades\Config;
use App\Modules\Transfer\Constants\TransferTransactionTypes;

class PollTransferService
{
    use UserLoggerTrait;

    private $dispatchDelayInSeconds = 180; // three minutes

    public static function instance()
    {
        $pollTransferService = new self;

        return $pollTransferService;
    }

    public function pollStoreOutboundRequest(array $params): void
    {
        $this->validateRequest($params['name'], false);
        $domainData = TransferDataQueryService::instance()->getOutboundDomainData($params['name']);
        $this->validateDomainData($params['name'], $domainData);
        
        if ($this->hasClientHoldStatusAndReject($domainData)) {
            return;
        }
        
        $this->storeOutboundRequest($domainData);
    }

    public function pollCancelledOutboundRequest(array $params): void
    {
        $this->validateRequest($params['name'], true);
        $domainData = TransferDataQueryService::instance()->getOutboundDomainData($params['name']);
        $this->validateDomainData($params['name'], $domainData);
        $this->cancelOutboundRequest($domainData);
    }

    public function pollRejectedInboundRequest(array $params): void
    {
        $domainData = TransferDataQueryService::instance()->getInboundDomainData($params['name']);
        $this->validateDomainData($params['name'], $domainData);

        app(AuthLogger::class)->info($this->fromWho('Client rejecting domain transfer start...', 'Cron:'));
        TransferRefundService::instance()->executeRefund($domainData, EppDomainStatus::TRANSFER_CLIENT_REJECTED);
        $this->handleSuccessfulClientRejectRefund($domainData, EppDomainStatus::TRANSFER_CLIENT_REJECTED);
        JobTransferService::instance()->addRetryLogs(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE, DomainStatus::ACTIVE, $params, $domainData->domain_id);
        app(AuthLogger::class)->info($this->fromWho('Client rejecting domain transfer end...', 'Cron:'));
        event(new DomainHistoryEvent([
            'domain_id' => $domainData->domain_id,
            'type' => 'TRANSFER_INBOUND_REJECTED',
            'user_id' => $domainData->user_id ?? null,
            'status' => 'success',
            'message' => 'Domain "'.$domainData->domain_name.'" inbound transfer rejected. Status changed from "'.$domainData->registered_domain_status,
            'payload' => $domainData,
        ]));

    }

    public function pollApprovedInboundRequest(array $params): void
    {
        $domainData = TransferDataQueryService::instance()->getInboundDomainData($params['name']);
        $this->validateDomainData($params['name'], $domainData);

        app(AuthLogger::class)->info($this->fromWho('Client approving domain transfer start...', 'Cron:'));
        $this->handleTransferredInDomain($domainData, $params['status']);
        TransferNotificationService::instance()->sendTransferPollApprovedNotif($domainData->domain_name, $domainData->user_id);
        JobTransferService::instance()->addRetryLogs(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE, DomainStatus::ACTIVE, $params, $domainData->domain_id);
        app(AuthLogger::class)->info($this->fromWho('Client approving domain transfer end...', 'Cron:'));
        event(new DomainHistoryEvent([
            'domain_id' => $domainData->domain_id,
            'type' => 'TRANSFER_INBOUND_APPROVED',
            'user_id' => $domainData->user_id ?? null,
            'status' => 'success',
            'message' => 'Domain "'.$domainData->domain_name.'" inbound transfer approved from Server. Transfer completed successfully.',
            'payload' => $domainData,
        ]));
    }

    public function pollServerApproved(array $params): void
    {
        $status = TransferDataQueryService::instance()->getRegisteredDomainStatus($params['name']);
        $this->validateDomainData($params['name'], $status);

        app(AuthLogger::class)->info($this->fromWho('Server approving outbound domain transfer start...', 'Cron:'));
        if ($status === UserDomainStatus::OWNED) {
            $this->outboundServerApproved($params);
        } elseif ($status === UserDomainStatus::RESERVED) {
            $this->inboundServerApproved($params);
        }
        app(AuthLogger::class)->info($this->fromWho('Server approving outbound domain transfer end...', 'Cron:'));
    }

    // PRIVATE Functions

    private function validateRequest(string $name, bool $mustExist): void
    {
        $requestExists = TransferDataQueryService::instance()->transferRequestExists($name);

        if ($mustExist === $requestExists) {
            return;
        }

        $message = $mustExist ? 'Request does not exist.' : 'Request already exists.';
        app(AuthLogger::class)->info($this->fromWho($message, 'Cron:'));
        throw new Exception(QueueErrorTypes::FAILED);
    }

    private function validateDomainData(string $name, string|stdClass|null $data): void
    {
        if (empty($data)) {
            app(AuthLogger::class)->info($this->fromWho('No data found for domain '.$name.'.', 'Cron:'));
            throw new Exception(QueueErrorTypes::FAILED);
        }
    }

    private function storeOutboundRequest(stdClass $domainData): void
    {
        app(AuthLogger::class)->info($this->fromWho('Storing domain transfer request start...', 'Cron:'));

        if ($this->hasCancellationRequestAndReject($domainData)) {
            return;
        }

        DomainService::instance()->updateDomainStatus($domainData->domain_id, DomainStatus::IN_PROCESS, false, 'Cron:');
        TransferDomainService::instance()->store($domainData->registered_domain_id);
        TransferNotificationService::instance()->sendPollTransferPendingNotif($domainData->domain_name, $domainData->user_id);
        $this->sendMail($domainData);
        app(AuthLogger::class)->info($this->fromWho('Storing domain transfer request end...', 'Cron:'));
        event(new DomainHistoryEvent([
            'domain_id' => $domainData->domain_id,
            'type' => 'TRANSFER_OUTBOUND_REQUEST',
            'user_id' => $domainData->user_id ?? null,
            'status' => 'success',
            'message' => 'Domain "'.$domainData->domain_name.'" outbound transfer request initiated by to Server. Domain status changed to in process.',
            'payload' => $domainData,
        ]));
    }

    private function hasClientHoldStatusAndReject(stdClass $domainData): bool
    {
        $domainInfo = EppDomainService::instance()->callEppDomainInfo($domainData->domain_name);
        if ($this->hasClientHoldStatus($domainInfo)) {
            app(AuthLogger::class)->info($this->fromWho('Domain has clientHold status, rejecting transfer request...', 'Cron:'));
            
            $response = EppTransferService::instance()->callEppTransferClientResponse($domainData->domain_name,TransferTransactionTypes::REJECT,'Cron:');

            if (JobTransferService::instance()->isSuccessful($response)) {
                DomainService::instance()->updateDomainStatus($domainData->domain_id, DomainStatus::ACTIVE);
                TransferDomainService::instance()->update($domainData->domain_name, $domainData->registered_domain_id, ['status' => TransferRequest::OUTBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_REJECTED], 'Cron:');                
                TransferNotificationService::instance()->sendTransferPollClientHoldNotif($domainData->domain_name, $domainData->user_id);
                app(AuthLogger::class)->info($this->fromWho('Successfully rejected transfer request for domain with clientHold status', 'Cron:'));
            } else {
                app(AuthLogger::class)->error($this->fromWho('Failed to reject transfer request', 'Cron:'));
                throw new Exception(QueueErrorTypes::RETRY);
            }

            JobTransferService::instance()->addRetryLogs(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE, DomainStatus::ACTIVE, ['name' => $domainData->domain_name], $domainData->domain_id);
            event(new DomainHistoryEvent(['domain_id' => $domainData->domain_id, 'type' => 'TRANSFER_OUTBOUND_REJECTED', 'user_id' => $domainData->user_id ?? null,'status' => 'success','message' => 'Domain "'.$domainData->domain_name.'" outbound transfer request rejected due to clientHold status.','payload' => $domainData
            ]));
            
            return true;
        }
        
        return false;
    }

    private function hasCancellationRequestAndReject(stdClass $domainData): bool
    {
        $cancellationRequest = $this->getCancellationRequestStatus($domainData->registered_domain_id);

        if (!$cancellationRequest) {
            return false;
        }

        if ($this->shouldRejectDueToCancellation($cancellationRequest)) {
            app(AuthLogger::class)->info($this->fromWho('Domain has cancellation request, rejecting transfer request...', 'Cron:'));

            $response = EppTransferService::instance()->callEppTransferClientResponse($domainData->domain_name, TransferTransactionTypes::REJECT, 'Cron:');

            if (JobTransferService::instance()->isSuccessful($response)) {
                TransferDomainService::instance()->update($domainData->domain_name, $domainData->registered_domain_id, ['status' => TransferRequest::OUTBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_REJECTED], 'Cron:');
                TransferNotificationService::instance()->sendTransferSystemRejectedNotif($domainData->domain_name, $domainData->user_id);
                app(AuthLogger::class)->info($this->fromWho('Successfully rejected transfer request for domain with cancellation request', 'Cron:'));
            } else {
                app(AuthLogger::class)->error($this->fromWho('Failed to reject transfer request', 'Cron:'));
                throw new Exception(QueueErrorTypes::RETRY);
            }

            JobTransferService::instance()->addRetryLogs(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE, DomainStatus::ACTIVE, ['name' => $domainData->domain_name], $domainData->domain_id);
            event(new DomainHistoryEvent([
                'domain_id' => $domainData->domain_id,
                'type' => 'TRANSFER_OUTBOUND_REJECTED',
                'user_id' => $domainData->user_id ?? null,
                'status' => 'success',
                'message' => 'Domain "'.$domainData->domain_name.'" outbound transfer request rejected due to cancellation request.',
                'payload' => $domainData
            ]));

            return true;
        }

        return false;
    }

    private function getCancellationRequestStatus(int $registeredDomainId): ?object
    {
        return DB::table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->first(['deleted_at', 'feedback_date']);
    }

    private function shouldRejectDueToCancellation(?object $cancellationRequest): bool
    {
        if (!$cancellationRequest) {
            return false;
        }

        $deletedAtIsNull = $cancellationRequest->deleted_at === null;
        $feedbackDateIsNull = $cancellationRequest->feedback_date === null;
        $deletedAtIsNotNull = $cancellationRequest->deleted_at !== null;
        $feedbackDateIsNotNull = $cancellationRequest->feedback_date !== null;

        $pendingDomains = $deletedAtIsNull && $feedbackDateIsNull;
        $approvedDomains = $deletedAtIsNotNull && $feedbackDateIsNotNull;

        return $approvedDomains || $pendingDomains;
    }

    private function hasClientHoldStatus(array $response): bool
    {
        if ($response['status'] !== Config::get('domain.status.ok')) {
            return false;
        }

        if (!isset($response['data']['status']) || !is_array($response['data']['status'])) {
            return false;
        }

        return in_array('clientHold', $response['data']['status']);
    }

    private function sendMail(stdClass $domainData): void
    {
        try {
            $user = DB::table('users')
                ->where('id', $domainData->user_id)
                ->select('email', 'first_name', 'last_name')
                ->first();

            if (!$user) {
                app(AuthLogger::class)->error($this->fromWho('Could not send transfer notification email - user not found', 'Cron:'));
                return;
            }

            $payload = [
                'email' => $user->email,
                'user_id' => $domainData->user_id,
                'name' => $domainData->user_name ?? $user->first_name.' '.$user->last_name,
                'domain_name' => $domainData->domain_name,
                'subject' => 'Domain Transfer Request Initiated',
                'request_date' => now()->toDateTimeString(),
                'greeting' => 'Dear '.($domainData->user_name ?? $user->first_name.' '.$user->last_name).',',
                'support_url' => config('app.url').Links::CONTACT_US_PAGE,
                'terms_url' => config('app.url').Links::TERMS_AND_CONDITIONS,
                'redirect_url' => route('transfer.outbound'),
                'phone' => MailDetails::PHONE_NUMBER,
                'sender_email' => config('mail.from.address'),
                'sender_name' => config('mail.from.sd_name')
            ];

            $queueMessage = (new OutboundRequestMail($payload))->onQueue(MailConstant::DOMAIN_TRANSFER);
            Mail::to($payload['email'])->send($queueMessage);

            event(new EmailSent($payload['user_id'], $payload['name'], $payload['email'], $payload['subject'], 'Domain Transfer - Outbound Request', json_encode($payload), null));

        } catch (Exception $e) {
            app(AuthLogger::class)->error($this->fromWho('Failed to send transfer notification email: '.$e->getMessage(), 'Cron:'));
        }
    }

    private function cancelOutboundRequest(stdClass $domainData): void
    {
        app(AuthLogger::class)->info($this->fromWho('Cancelling domain transfer request start...', 'Cron:'));
        DomainService::instance()->updateDomainStatus($domainData->domain_id, DomainStatus::ACTIVE, false, 'Cron:');
        TransferDomainService::instance()->update($domainData->domain_name, $domainData->registered_domain_id, [
            'status' => TransferRequest::OUTBOUND.'.'.EppDomainStatus::TRANSFER_CLIENT_CANCELLED,
        ], 'Cron:');
        TransferNotificationService::instance()->sendTransferPollCancelledNotif($domainData->domain_name, $domainData->user_id);
        app(AuthLogger::class)->info($this->fromWho('Cancelling domain transfer request end...', 'Cron:'));
    }

    private function handleSuccessfulClientRejectRefund($domainData, string $status): void
    {
        DomainService::instance()->deleteDomain($domainData->domain_id, DomainStatus::FAILED, UserDomainStatus::DELETED, 'Cron:');
        TransferDomainService::instance()->update($domainData->domain_name, $domainData->registered_domain_id, ['status' => TransferRequest::INBOUND.'.'.$status], 'Cron:');
        TransferNotificationService::instance()->sendTransferPollRejectedNotif($domainData->domain_name, $domainData->user_id);
    }

    private function validateDatastoreApprove(array $response): void
    {
        if (JobTransferService::instance()->isSuccessful($response)) {
            return;
        }

        app(AuthLogger::class)->error($this->fromWho('Datastore approving domain transfer has failed. Job has been added to the retry logs.', 'Cron:'));
        throw new Exception(QueueErrorTypes::FAILED);
    }

    private function inboundServerApproved(array $params): void
    {
        $domainData = TransferDataQueryService::instance()->getInboundDomainData($params['name']);
        $this->validateDomainData($params['name'], $domainData);
        $this->handleTransferredInDomain($domainData, $params['status']);

        TransferNotificationService::instance()->sendTransferServerApprovedNotif($domainData->domain_name, $domainData->user_id, $domainData->registered_domain_status);
        JobTransferService::instance()->addRetryLogs(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE, DomainStatus::ACTIVE, $params, $domainData->domain_id);
    }

    private function outboundServerApproved(array $params): void
    {
        $domainData = TransferDataQueryService::instance()->getOutboundDomainData($params['name']);
        $this->validateDomainData($params['name'], $domainData);
        $this->handleTransferredOutDomain($domainData, $params['status']);

        TransferNotificationService::instance()->sendTransferServerApprovedNotif($domainData->domain_name, $domainData->user_id, $domainData->registered_domain_status);
        JobTransferService::instance()->addRetryLogs(QueueConnection::DOMAIN_TRANSFER_POLL_UPDATE, DomainStatus::ACTIVE, $params, $domainData->domain_id);
    }

    private function handleTransferredInDomain(stdClass $domainData, string $status): void
    {
        $response = EppTransferService::instance()->callDatastoreTransferApproved($domainData->domain_name, TransferRequest::INBOUND.'.'.$status);
        $this->validateDatastoreApprove($response);
        $transferredIn = $response['data']['property']['transferredIn'];
        EppTransferService::instance()->domainTransferUpdate($domainData, $transferredIn);
        PaymentNodeService::instance()->updatePaymentNodeStatus($domainData->registered_domain_id, PaymentNodeStatus::COMPLETED, 'Cron:');
        TransferDomainService::instance()->update($domainData->domain_name, $domainData->registered_domain_id, ['status' => TransferRequest::INBOUND.'.'.$status], 'Cron:');
        ScheduleDomainExpiryNotice::dispatch($domainData->user_id)->delay($this->dispatchDelayInSeconds);
    }

    private function handleTransferredOutDomain(stdClass $domainData, string $status): void
    {
        $response = EppTransferService::instance()->callDatastoreTransferApproved($domainData->domain_name, TransferRequest::OUTBOUND.'.'.$status);
        $this->validateDatastoreApprove($response);
        $transferredOut = $response['data']['response']['transferredOut'];

        DomainService::instance()->deleteDomain($domainData->domain_id, DomainStatus::TRANSFERRED, UserDomainStatus::TRANSFERRED, 'Cron:');
        DB::table('domains')->where('id', $domainData->domain_id)->update(['transferredOut' => $transferredOut]);
        TransferDomainService::instance()->update($domainData->domain_name, $domainData->registered_domain_id, ['status' => TransferRequest::OUTBOUND.'.'.EppDomainStatus::TRANSFER_SERVER_APPROVED], 'Cron:');
        ScheduleDomainExpiryNotice::dispatch($domainData->user_id)->delay($this->dispatchDelayInSeconds);
    }
}
