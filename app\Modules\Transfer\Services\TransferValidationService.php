<?php

namespace App\Modules\Transfer\Services;

use Illuminate\Support\Facades\DB;

class TransferValidationService
{
    public static function instance(): self
    {
        return new self;
    }

    public function hasCancellationRequest(int $registeredDomainId): bool
    {
        $cancellationRequest = $this->getCancellationRequestStatus($registeredDomainId);
        
        if (!$cancellationRequest) {
            return false;
        }
        
        return $this->shouldRejectDueToCancellation($cancellationRequest);
    }

    private function getCancellationRequestStatus(int $registeredDomainId): ?object
    {
        return DB::table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->first(['deleted_at', 'feedback_date']);
    }

    private function shouldRejectDueToCancellation(?object $cancellationRequest): bool
    {
        if (!$cancellationRequest) {
            return false;
        }

        $deletedAtIsNull = $cancellationRequest->deleted_at === null;
        $feedbackDateIsNull = $cancellationRequest->feedback_date === null;
        $deletedAtIsNotNull = $cancellationRequest->deleted_at !== null;
        $feedbackDateIsNotNull = $cancellationRequest->feedback_date !== null;

        $pendingDomains = $deletedAtIsNull && $feedbackDateIsNull;
        $approvedDomains = $deletedAtIsNotNull && $feedbackDateIsNotNull;

        return $approvedDomains || $pendingDomains;
    }
}
