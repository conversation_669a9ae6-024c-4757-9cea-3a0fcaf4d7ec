<?php

namespace App\Modules\Domain\Services;

use App\Models\Domain;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\Domain\Constants\DomainStatus;
use App\Modules\Domain\Constants\UserDomainStatus;
use App\Modules\Domain\Jobs\RequestDomainAuthCode;
use App\Modules\Notification\Services\DomainNotificationService;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DomainDeletionService
{
    use UserLoggerTrait;

    public static function instance(): self
    {
        $domainDeletionService = new self;

        return $domainDeletionService;
    }

    public function query(){
        return DB::table('domain_cancellation_requests')
            ->join('registered_domains', 'registered_domains.id', '=', 'domain_cancellation_requests.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->select(
                'domain_cancellation_requests.*',
                'domains.id as domain_id',
                'domains.name as domain_name',
                'domains.status as domain_status',
                'user_contacts.user_id',
                'registered_domains.id as registered_domain_id'
            );
    }
}